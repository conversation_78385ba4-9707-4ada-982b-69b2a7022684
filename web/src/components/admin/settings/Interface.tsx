import React from 'react';

interface InterfaceProps {
  onSave?: () => void;
}

export default function Interface({ onSave }: InterfaceProps) {
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
        Interface Settings
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        Interface settings content will be implemented here.
      </p>
    </div>
  );
}