import React from 'react';

interface WebSearchProps {
  saveHandler?: () => void;
}

export default function WebSearch({ saveHandler }: WebSearchProps) {
  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
        Web Search Settings
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        Web search settings content will be implemented here.
      </p>
    </div>
  );
}